@use 'colors' as *;

.partners {
    margin-top: 2rem;
    padding: 3rem 0;
    background-color: $bg-tertiary;
    border-top: 1px solid $border-medium;

}

.partners-header {
    text-align: center;
}

.partners-list {
    display: flex;
    justify-content: center;
    align-items: center;;
    flex-wrap: nowrap;
    gap: 3rem;
    margin-top: 2rem;

    .partner-name {
        font-weight: bold;
        font-size: 1.2rem;
    }

    img {
        max-width: 150px;
        max-height: 60px;
        filter: grayscale(100%);
    }
}

footer {
    background-color: $brand-green-primary;
    padding: 3rem 0;

    .container {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 2rem;
        grid-template-areas:
            "info contact finance"
            "copyright copyright copyright";
    }
    .club-info {
        grid-area: info;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        img {
            max-width: 100%;
            height: auto;
        }

        h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            color: $text-primary;
        }

        p {
            font-size: 0.875rem;
            line-height: 1.5;
            color: $text-secondary;
        }
    }

    .social-links {
        grid-area: social;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        a {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: $text-primary;
            text-decoration: none;
            font-size: 0.875rem;
            line-height: 1.5;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
                color: $brand-green-light;

                .social-icon {
                    background-color: $brand-green-light;
                    color: $brand-green-primary;
                    transform: scale(1.05);
                }
            }
        }

        .social-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: rgba($color-white, 0.1);
            border-radius: 12px; // Squircle effect
            color: $text-primary;
            transition: all 0.3s ease;
            flex-shrink: 0;

            svg {
                width: 20px;
                height: 20px;
            }
        }
    }

    .contact {
        grid-area: contact;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            color: $text-primary;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;

            .contact-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                background-color: $brand-green-light;
                border-radius: 8px;
                color: $brand-green-primary;
                flex-shrink: 0;
                margin-top: 0.125rem; // Slight offset to align with text

                svg {
                    width: 16px;
                    height: 16px;
                }
            }

            div {
                flex: 1;

                p {
                    margin: 0 0 0.25rem 0;
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: $text-primary;
                }

                address {
                    font-style: normal;

                    p {
                        margin: 0;
                        font-size: 0.875rem;
                        line-height: 1.4;
                        color: $text-secondary;
                        font-weight: 400;
                    }
                }

                a {
                    color: $text-primary;
                    text-decoration: none;
                    font-size: 0.875rem;
                    line-height: 1.4;
                    font-weight: 400;

                    &:hover {
                        color: $brand-green-primary;
                        text-decoration: underline;
                    }
                }
            }
        }
    }

    .finance {
        grid-area: finance;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            color: $text-primary;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;

            .contact-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                background-color: $brand-green-light;
                border-radius: 8px;
                color: $brand-green-primary;
                flex-shrink: 0;
                margin-top: 0.125rem; // Slight offset to align with text

                svg {
                    width: 16px;
                    height: 16px;
                }
            }

            div {
                flex: 1;

                p {
                    margin: 0 0 0.25rem 0;
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: $text-primary;
                }

                address {
                    font-style: normal;

                    p {
                        margin: 0;
                        font-size: 0.875rem;
                        line-height: 1.4;
                        color: $text-secondary;
                        font-weight: 400;
                    }
                }

                a {
                    color: $text-primary;
                    text-decoration: none;
                    font-size: 0.875rem;
                    line-height: 1.4;
                    font-weight: 400;

                    &:hover {
                        color: $brand-green-primary;
                        text-decoration: underline;
                    }
                }
            }
        }
    }

    .copyright {
        grid-area: copyright;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        p {
            font-size: 0.875rem;
            line-height: 1.5;
            color: $text-secondary;
        }

        div {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: $text-primary;
            text-decoration: none;
            font-size: 0.875rem;
            line-height: 1.5;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }

}
