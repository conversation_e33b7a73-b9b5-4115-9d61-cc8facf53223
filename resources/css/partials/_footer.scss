@use 'colors' as *;

.partners {
    margin-top: 2rem;
    padding: 3rem 0;
    background-color: $bg-tertiary;
    border-top: 1px solid $border-medium;

}

.partners-header {
    text-align: center;
}

.partners-list {
    display: flex;
    justify-content: center;
    align-items: center;;
    flex-wrap: nowrap;
    gap: 3rem;
    margin-top: 2rem;

    .partner-name {
        font-weight: bold;
        font-size: 1.2rem;
    }

    img {
        max-width: 150px;
        max-height: 60px;
        filter: grayscale(100%);
    }
}

footer {
    background-color: $brand-green-primary;
    padding: 3rem 0;

    .container {
        display: grid;
        w
        grid-template-columns: 1fr 1fr 1fr;
        gap: 2rem;
        grid-template-areas:
            "info contact finance"
            "copyright copyright copyright";
    }
    .club-info {
        grid-area: info;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        img {
            max-width: 100%;
            height: auto;
        }

        h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            color: $text-primary;
        }

        p {
            font-size: 0.875rem;
            line-height: 1.5;
            color: $text-secondary;
        }
    }

    .social-links {
        grid-area: contact;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: $text-primary;
            text-decoration: none;
            font-size: 0.875rem;
            line-height: 1.5;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .contact {
        grid-area: contact;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            color: $text-primary;
        }

        div {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: $text-primary;
            text-decoration: none;
            font-size: 0.875rem;
            line-height: 1.5;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .finance {
        grid-area: finance;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            color: $text-primary;
        }

        div {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: $text-primary;
            text-decoration: none;
            font-size: 0.875rem;
            line-height: 1.5;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .copyright {
        grid-area: copyright;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        p {
            font-size: 0.875rem;
            line-height: 1.5;
            color: $text-secondary;
        }

        div {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: $text-primary;
            text-decoration: none;
            font-size: 0.875rem;
            line-height: 1.5;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }

}
