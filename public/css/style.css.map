{"version": 3, "file": "css/style.css", "mappings": "AAAA;;;6CAAA;ACAA;;6CAAA;ACAA;;;6CAAA;AAKA;;6CAAA;AAmBA;;6CAAA;AAoBA;;6CAAA;ADtCA;AACA;EACI;ADeJ;;ACZA;AACA;EACI;EACA;ADeJ;;ACZA;EACI;EACA;EAEA;EACA;EACA;EACA;EACA;EACA,cCeO;EDbP;EACA;EACA;EAEA;EACA,yBCEO;AFUX;;ACVA;EACI;ADaJ;;ACVA;AACA;EACI;EACA;ADaJ;;ACVA;;;6CAAA;AAKA;AACA;EACI;EACA;EAEA;ADWJ;ACVI;EACI;ADYR;;ACRA;EACI;EACA;EACA;EACA;EACA;ADWJ;;ACRA;EACI;EACA;EACA;EACA;ADWJ;;ACRA;EACI;EACA;EACA;EACA;ADWJ;;ACRA;EACI;EACA;EACA;ADWJ;;ACRA;EACI;EACA;EACA;ADWJ;;ACRA;EACI;EACA;EACA;ADWJ;;ACRA;AACA;EACI;EACA;EACA;EACA;ADWJ;ACTI;EACI;ADWR;;ACPA;;6CAAA;AAIA;AACA;EACI;IACI;IACA;EDSN;ECNE;IACI;IACA;EDQN;ECLE;IACI;IACA;EDON;ECJE;IACI;IACA;EDMN;AACF;ACHA;AACA;EACI;IACI;IACA;EDKN;ECFE;IACI;IACA;EDIN;ECDE;IACI;IACA;EDGN;AACF;ACAA;;6CAAA;AAIA;AACA;EACI;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EAMA;EAOA;EAMA;ADnBJ;ACCI;EACI;EACA;ADCR;ACGI;EACI;EACA;EACA;ADDR;ACKI;EACI;EACA;ADHR;ACOI;EAEI;EACA;EACA;ADNR;ACQQ;EACI;EACA;ADNZ;;ACWA;;6CAAA;AAIA;AACA;;EAEI,yBCrNgB;EDsNhB,cCxMU;AF+Ld;ACWI;;EACI,yBC7NY;ED8NZ,cC/Nc;EDgOd;ADRR;;ACaA;AACA;;EAEI,yBCvOgB;EDwOhB,cCzOkB;AF+NtB;ACaI;;EACI,yBC7Oc;ED8Od,cC3NM;ED4NN;ADVR;;ACeA;AACA;;EAEI,yBCnOU;EDoOV,cCrOU;AFyNd;ACcI;;EACI,yBCtPY;EDuPZ,cCzOM;ED0ON;ADXR;;ACgBA;;6CAAA;AAIA;AACA;;EAEI;EACA;ADdJ;;ACiBA;;EAEI;EACA;ADdJ;;ACiBA;AACA;EACI;EACA;EACA,cCvRkB;EDwRlB;ADdJ;ACgBI;EACI,yBC3Rc;ED4Rd,cCzQM;ED0QN;EAEA,qBC/Rc;AFgRtB;ACkBI;EACI;EACA;EACA;ADhBR;;ACoBA;EACI;EACA;EACA;EACA;ADjBJ;ACmBI;EACI;EACA;EACA;EAEA;ADlBR;;ACsBA;AACA;;EAEI;ADnBJ;;ACsBA;;EAEI;ADnBJ;;AGvTA;;6CAAA;AAMA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;AHmTJ;AGlTI;EACI;AHoTR;AGjTI;EACI;AHmTR;;AG/SA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;AH+SJ;AG9SI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EAiBA;AH+RR;AG/SQ;;EAEI;EACA;AHiTZ;AG9SQ;EACI;EACA;AHgTZ;AG7SQ;EACI;EACA;AH+SZ;AG3SQ;EACI;EACA;EACA;EACA;EACA;EACA;AH6SZ;AG1SQ;EACI;AH4SZ;;AGvSA;;6CAAA;AAIA;EACI;IACI;IACA;IACA;IAEA;IAGA;IAGA;IACA;IACA;IAEA;EHqSN;EGpSM;IACI;IACA;IACA;EHsSV;AACF;AGlSA;;6CAAA;AAIA;AACA;EACI;EACA;EACA;AHmSJ;AGjSI;EACI;EACA;EACA;KAAA;AHmSR;;AG/RA;AACA;EACI;EACA;EACA;EACA;AHkSJ;;AG/RA;AACA;EACI;AHkSJ;AGhSI;EACI;EACA;EACA;EACA;EACA,yBD5IM;EC6IN,cD9IM;EC+IN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHkSR;AGhSQ;EACI,yBD1KQ;EC2KR,cD7JE;EC8JF;EACA;AHkSZ;AG9RQ;EACI;EACA;EACA;AHgSZ;AG7RQ;EACI;EACA;AH+RZ;;AG1RA;AACA;EACI;AH6RJ;;AG1RA;AACA;EACI;AH6RJ;;AG1RA;;6CAAA;AAIA;AACA;;EAEI;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EAOA;EAMA;AH4QJ;AGxRI;;EACI;EACA;EACA;AH2RR;AGvRI;;EACI;EACA;AH0RR;AGtRI;;EACI;EACA;AHyRR;;AGlRI;;EACI;EACA;AHsRR;AGjRQ;;EACI;EACA;AHoRZ;AGhRQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHmRZ;AG9QI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHiRR;;AG5QA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH+QJ;AG5QI;EAjBJ;IAkBQ;IACA;EH+QN;AACF;AG5QI;EAEI;EACA;EACA;AH6QR;;AGxQA;EACI;EACA;EACA;EACA;AH2QJ;;AGvQA;EACI;AH0QJ;;AGvQA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;AHwQJ;AGtQI;;EACI;EAEA;EACA;AHwQR;AGrQI;;EAEI;EACA;AHuQR;AGpQI;;EACI;EACA;EACA;AHuQR;;AGnQA;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;AHkQJ;AGhQI;EACI;EAEA;EACA;AHiQR;AG9PI;EAEI;EACA;AH+PR;AG5PI;EACI;EACA;AH8PR;;AG1PA;EACI;EACA;EACA;EACA;EACA;EACA;AH6PJ;;AG1PA;EACI;EACA;EACA;EACA;EACA;AH6PJ;;AG1PA;EACI;EACA;EACA;AH6PJ;;AGvPQ;EACI;EACA;AH0PZ;AGxPQ;EAEI;AHyPZ;AGvPQ;EACI;EACA;AHyPZ;;AGnPA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AHsPJ;AGnPI;EACI;EACA;EACA;AHqPR;;AGjPA;EACI;EACA;AHoPJ;;AGhPA;EACI;EACA;EACA;EACA;EACA;AHmPJ;AGhPI;EACI;AHkPR;AG/OQ;EAAiB;AHkPzB;AGjPQ;EAAiB;AHoPzB;AGnPQ;EAAiB;AHsPzB;AGrPQ;EAAiB;AHwPzB;AGvPQ;EAAiB;AH0PzB;AGzPQ;EAAiB;AH4PzB;AG3PQ;EAAiB;AH8PzB;AG7PQ;EAAiB;AHgQzB;;AG5PA;EACI;IACI;IACA;EH+PN;AACF;AG3PA;;EAEI;EACA;EACA;AH6PJ;;AG1PA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;AH6PJ;AG3PI;;EACI;AH8PR;AG3PI;;EACI;EACA;AH8PR;;AGzPA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;AH0PJ;AGxPI;;EACI;EAEA;EACA;AH0PR;AGvPI;;EAEI;EACA;AHyPR;AGtPI;;EACI;EACA;AHyPR;;AGlPI;;EACI;EACA;AHsPR;AGjPQ;;EACI;EACA;AHoPZ;AGhPQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHmPZ;AG9OI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHiPR;;AG3OA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH8OJ;AG5OI;EACI;AH8OR;AG3OI;EACI;EACA;AH6OR;;AGzOA;EACI;EACA;AH4OJ;;AGzOA;;EAEI;EACA;EACA;AH4OJ;;AGzOA;EACI;EACA;EACA;AH4OJ;;AGxOA;;EAEI;EACA;EACA;AH2OJ;AGzOI;;EACI;AH4OR;;AGxOA;EACI;AH2OJ;;AGvOA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AH0OJ;AGvOI;EACI;AHyOR;;AGrOA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHwOJ;AGtOI;EACI;EACA;EACA;EACA;AHwOR;AGpOI;EACI;EACA;EACA;AHsOR;AGnOI;EACI;EACA;AHqOR;;AGhOA;EACI;IACI;EHmON;AACF;AG/NA;EACI;IACI;EHiON;AACF;AG7NA;EACI;IACI;EH+NN;EG5NE;IACI;EH8NN;EG1NE;IACI;EH4NN;EG1NM;IACI;EH4NV;EGxNM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EH0NV;EGpNM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;EHsNV;AACF;AGlNA;EACI;IACI;EHoNN;EGjNE;IACI;EHmNN;AACF;AG/MA;EAGQ;IACI;IACA;IACA;EH+MV;EG3MM;IACI;IACA;IACA;EH6MV;AACF;AGxMA;EAEI;EACA;EACA;AHyMJ;AGvMI;EACI;AHyMR;;AGpMA;;;EAGI;EACA;EACA;AHuMJ;;AI5iCA;;6CAAA;AAMA;EACI;EACA;EACA;EACA;EAWA;EA8BA;AJqgCJ;AI5iCI;EACI;AJ8iCR;AI5iCI;EACI;IACI;EJ8iCV;AACF;AI1iCI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAGA;EAEA;EACA,cFnBM;AF0jCd;AIniCI;EACI;EACA;EACA;EACA;AJqiCR;;AIjiCA;;6CAAA;AAIA;EACI;EACA;EAEA;EAEA;AJiiCJ;AIhiCI;EAPJ;IAQQ;IACA;EJmiCN;AACF;;AK7mCA;AAGA;EACI;EACA;EACA;EAEA;EACA;EACA,sCACI;EAGJ;AL0mCJ;AKzmCI;EAZJ;IAaQ;IACA;EL4mCN;AACF;AK1mCI;EACI;EACA;AL4mCR;;AKxmCA;AACA;EACI;AL2mCJ;AKzmCI;EACI;EACA;EACA;EACA;EACA;EACA;AL2mCR;AKzmCQ;EACI;EACA;AL2mCZ;AKxmCQ;EACI;EACA;EACA;EACA;EAqBA;ALslCZ;AKzmCY;EACI;EACA;EACA;AL2mChB;AKzmCgB;EALJ;IAMQ;EL4mClB;AACF;AKzmCY;EACI;EACA;EACA;KAAA;EACA;EACA;EACA;AL2mChB;AKvmCY;EACI,mBH5DM;EG6DN;EACA;EACA;EACA;EACA;ALymChB;;AKnmCA;AACA;EACI;EACA;EACA;EACA;EACA;EACA,cH3DU;EG4DV;ALsmCJ;AKpmCI;EACI;EACA;EACA;EACA;EACA;ALsmCR;AKnmCI;EACI;EACA;EACA;ALqmCR;AKnmCQ;EALJ;IAMQ;ELsmCV;AACF;AKnmCI;EACI;EACA;EACA;ALqmCR;;AKjmCA;AACA;EACI;EAyBA;EAQA;EAwBA;EAuDA;EAkCA;ALu9BJ;AKvmCI;EACI;EACA;EACA;EACA;ALymCR;AKvmCQ;EACI;EACA,cHtHU;AF+tCtB;AKtmCQ;EACI,cHrHQ;EGsHR;EACA;EACA;ALwmCZ;AKtmCY;EACI;ALwmChB;AKlmCI;EACI,mBHpHM;EGqHN;EACA;EACA;ALomCR;AKhmCI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALkmCR;AKhmCQ;EACI;ALkmCZ;AK/lCQ;EACI;EACA;EACA;EACA;EACA;ALimCZ;AK5lCI;EACI;EACA;EACA;EACA,mBH1Kc;EG2Kd;EACA;EACA;EACA;EACA;EACA,cH7JM;EG8JN;EAEA;AL6lCR;AK3lCY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AL6lChB;AK1lCY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AL4lChB;AKxlCQ;EACI;EACA;EACA;AL0lCZ;AKvlCQ;EACI;EACA;EACA;EACA;ALylCZ;AKplCI;EACI;EACA;ALslCR;AKplCQ;EACI;EACA;EACA;EACA,cHtMD;EGuMC;EAEA;EACA;EACA;EACA;ALqlCZ;AKllCQ;EACI;EACA;ALolCZ;AKllCY;EACI;EACA,WHvNL;AF2yCX;AKllCgB;EACI;EACA,cHxPE;AF40CtB;AK7kCI;EACI;EACA;AL+kCR;;AK3kCA;AACA;EACI;EAQA;EAwBA;EAqEA;EAOA;EA0BA;EA+BA;EAgCA;EA8CA;ALm2BJ;AKplCI;EACI;EACA;EACA;ALslCR;AKllCI;EACI;EACA;EACA;EACA;ALolCR;AKllCQ;EACI;EACA,cHxRU;EGyRV;ALolCZ;AKjlCQ;EACI;EACA;EACA;ALmlCZ;AKjlCY;EALJ;IAMQ;ELolCd;AACF;AK/kCI;EACI;EACA;EACA,mBHxRM;EGyRN;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EA0BA;EAaA;EAMA;ALsiCR;AKjlCQ;EACI,yBH1TQ;EG2TR,cH5TU;EG6TV;EACA;ALmlCZ;AKhlCQ;EACI;EACA;ALklCZ;AK/kCQ;EACI;EACA;ALilCZ;AK9kCQ;EACI;EACA;EACA;EACA;ALglCZ;AK5kCQ;EACI;EACA;EACA;EACA,yBHnUE;EGoUF;AL8kCZ;AK3kCQ;EACI,yBH5VU;AFy6CtB;AKzkCQ;EACI;UAAA;EACA;UAAA;AL2kCZ;AKvkCQ;EACI;UAAA;EACA;UAAA;ALykCZ;AKpkCI;EACI;EACA;EACA;ALskCR;AKlkCI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;ALmkCR;AKlkCQ;EACI;ALokCZ;AKjkCQ;EAlBJ;IAmBQ;IACA;IACA;ELokCV;AACF;AKhkCI;EACI;EACA,mBH7XM;EG8XN;EAEA;EACA;EACA;ALikCR;AK/jCQ;EATJ;IAUQ;ELkkCV;AACF;AKhkCQ;EAbJ;IAcQ;ELmkCV;AACF;AKjkCQ;EACI;EACA;ALmkCZ;AKhkCQ;EACI;EACA;EACA;EACA;ALkkCZ;AK7jCI;EACI;EACA;EACA,mBHzZE;EG0ZF;AL+jCR;AK7jCQ;EANJ;IAOQ;ELgkCV;AACF;AK9jCQ;EACI;EACA;EACA;KAAA;ALgkCZ;AK7jCQ;EACI;EACA;EACA;EACA;EACA;EACA,mBHncU;EGocV,cHjbE;AFg/Cd;AK7jCY;EACI;AL+jChB;AKzjCI;EACI;AL2jCR;AKzjCQ;EAHJ;IAIQ;EL4jCV;AACF;AK1jCQ;EACI;EACA;EACA,WH1bD;EG2bC;EACA;EACA;EACA;AL4jCZ;AKzjCQ;EACI;EACA;EACA;EACA;EACA,cHpcD;EGscC;EACA;EACA;EACA;EACA;AL0jCZ;AKvjCQ;EACI;EACA;EACA,WHldD;EGmdC;EAEA;EACA;EACA;EACA;EACA;ALwjCZ;AKnjCI;EACI,mBHvfY;EGwfZ,cH1eM;AF+hDd;AKnjCQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALqjCZ;AKnjCY;EAVJ;IAWQ;IACA;ELsjCd;AACF;AKnjCQ;EACI;EACA;ALqjCZ;AKnjCY;EACI;EACA;ALqjChB;AKjjCQ;EACI;EACA;EACA;EACA,cH1gBE;AF6jDd;AKhjCQ;EACI;EACA;EACA;EACA;EACA,cHlhBE;AFokDd;AK/iCQ;EACI;EACA;EACA;EACA,cHzhBE;AF0kDd;;AMrmDA;EACI;EACA;EACA,yBJ6BO;EI5BP;ANwmDJ;;AMpmDA;EACI;ANumDJ;;AMpmDA;EACI;EACA;EACA;EACA;EACA;EACA;ANumDJ;AMrmDI;EACI;EACA;ANumDR;AMpmDI;EACI;EACA;EACA;ANsmDR;;AMlmDA;EACI,yBJzBkB;EI0BlB;ANqmDJ;AMnmDI;EACI;EACA;EACA;EACA,2EACI;ANomDZ;AMjmDI;EACI;EACA;EACA;EACA;ANmmDR;AMjmDQ;EACI;EACA;ANmmDZ;AMhmDQ;EACI;EACA;EACA;EACA,cJpBD;AFsnDX;AM/lDQ;EACI;EACA;EACA,WJ5BD;AF6nDX;AM7lDI;EACI;EACA;EACA;EACA;AN+lDR;AM7lDQ;EACI;EACA;EACA;EACA,cJxCD;EIyCC;EACA;EACA;EACA;EACA;AN+lDZ;AM7lDY;EACI,cJ9EI;AF6qDpB;AM7lDgB;EACI,yBJjFA;EIkFA,cJnFE;EIoFF;AN+lDpB;AM1lDQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA,cJlED;EImEC;EACA;AN4lDZ;AM1lDY;EACI;EACA;AN4lDhB;AMvlDI;EACI;EACA;EACA;EACA;ANylDR;AMvlDQ;EACI;EACA;EACA;EACA,cJvFD;AFgrDX;AMtlDQ;EACI;EACA;EACA;ANwlDZ;AMtlDY;EACI;EACA;EACA;EACA;EACA;EACA,yBJnII;EIoIJ;EACA,cJtIM;EIuIN;EACA;ANwlDhB;AMtlDgB;EACI;EACA;ANwlDpB;AMplDY;EACI;ANslDhB;AMplDgB;EACI;EACA;EACA;EACA,cJxHT;AF8sDX;AMnlDgB;EACI;ANqlDpB;AMnlDoB;EACI;EACA;EACA;EACA,WJpIb;EIqIa;ANqlDxB;AMjlDgB;EACI,cJxIT;EIyIS;EACA;EACA;EACA;ANmlDpB;AMjlDoB;EACI,cJ9KF;EI+KE;ANmlDxB;AM5kDI;EACI;EACA;EACA;EACA;AN8kDR;AM5kDQ;EACI;EACA;EACA;EACA,cJjKD;AF+uDX;AM3kDQ;EACI;EACA;EACA;AN6kDZ;AM3kDY;EACI;EACA;EACA;EACA;EACA;EACA,yBJ7MI;EI8MJ;EACA,cJhNM;EIiNN;EACA;AN6kDhB;AM3kDgB;EACI;EACA;AN6kDpB;AMzkDY;EACI;AN2kDhB;AMzkDgB;EACI;EACA;EACA;EACA,cJlMT;AF6wDX;AMxkDgB;EACI;AN0kDpB;AMxkDoB;EACI;EACA;EACA;EACA,WJ9Mb;EI+Ma;AN0kDxB;AMtkDgB;EACI,cJlNT;EImNS;EACA;EACA;EACA;ANwkDpB;AMtkDoB;EACI,cJxPF;EIyPE;ANwkDxB;AMjkDI;EACI;EACA;EACA;EACA;ANmkDR;AMjkDQ;EACI;EACA;EACA,WJ5OD;AF+yDX;AMhkDQ;EACI;EACA;EACA;EACA,cJjPD;EIkPC;EACA;EACA;EACA;ANkkDZ;AMhkDY;EACI;ANkkDhB,C", "sources": ["webpack:///./resources/css/style.scss", "webpack:///./resources/css/partials/_basics.scss", "webpack:///./resources/css/partials/_colors.scss", "webpack:///./resources/css/partials/_nav.scss", "webpack:///./resources/css/partials/_hero.scss", "webpack:///./resources/css/partials/_highlighted-updates.scss", "webpack:///./resources/css/partials/_footer.scss"], "sourcesContent": ["/* ========================================\n   OK Tyr Website - Main SCSS Entry Point\n   Mobile-first responsive design system\n   ======================================== */\n\n// Core foundation styles (must be first)\n@use 'partials/basics';\n@use 'partials/colors';\n@use 'partials/grid';\n\n// Component styles\n@use 'partials/nav';\n@use 'partials/hero';\n@use 'partials/highlighted-updates';\n@use 'partials/footer';\n", "/* ========================================\n   Foundation Styles - Reset & Base Setup\n   ======================================== */\n\n@use 'colors' as *;\n\n/* CSS Reset & Box Model */\n* {\n    box-sizing: border-box;\n}\n\n/* Base HTML & Body Setup */\nhtml {\n    font-size: 16px; // Base font size for rem calculations\n    line-height: 1.5;\n}\n\nbody {\n    margin: 0;\n    padding: 0;\n\n    /* Typography Foundation */\n    font-family: 'Inter', sans-serif;\n    font-weight: 400;\n    font-size: 1rem;\n    line-height: 1.6;\n    color: $text-primary;\n\n    /* Font Rendering */\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n\n    /* Background */\n    background-color: $bg-secondary;\n}\nsection {\n    padding: 8px;\n}\n\n/* Layout Container */\n.container {\n    max-width: 1440px;\n    margin: 40px auto;\n}\n\n/* ========================================\n   Typography System - Inter Font Scale\n   Based on 1.25 (Major Third) scale with optical adjustments\n   ======================================== */\n\n/* Heading Hierarchy */\nh1, h2, h3, h4, h5, h6 {\n    font-weight: 600;\n    margin: 0;\n\n    /* Remove margin from last child */\n    &:last-child {\n        margin-bottom: 0;\n    }\n}\n\nh1 {\n    font-size: 2.25rem;   // 36px\n    line-height: 1.2;\n    font-weight: 700;\n    letter-spacing: -0.01em;\n    margin-bottom: 1.5rem;\n}\n\nh2 {\n    font-size: 1.875rem;  // 30px\n    line-height: 1.25;\n    letter-spacing: -0.01em;\n    margin-bottom: 1.25rem;\n}\n\nh3 {\n    font-size: 1.5rem;    // 24px\n    line-height: 1.3;\n    letter-spacing: -0.005em;\n    margin-bottom: 1rem;\n}\n\nh4 {\n    font-size: 1.25rem;   // 20px\n    line-height: 1.35;\n    margin-bottom: 1rem;\n}\n\nh5 {\n    font-size: 1.125rem;  // 18px\n    line-height: 1.4;\n    margin-bottom: 0.75rem;\n}\n\nh6 {\n    font-size: 1rem;      // 16px\n    line-height: 1.45;\n    margin-bottom: 0.75rem;\n}\n\n/* Body Text */\np {\n    font-size: 1rem;      // 16px\n    line-height: 1.6;\n    font-weight: 400;\n    margin: 0 0 1rem 0;\n\n    &:last-child {\n        margin-bottom: 0;\n    }\n}\n\n/* ========================================\n   Responsive Typography Adjustments\n   ======================================== */\n\n/* Tablet & Mobile Adjustments */\n@media (max-width: 768px) {\n    h1 {\n        font-size: 1.875rem;  // 30px on tablet/mobile\n        line-height: 1.2;\n    }\n\n    h2 {\n        font-size: 1.5rem;    // 24px on tablet/mobile\n        line-height: 1.25;\n    }\n\n    h3 {\n        font-size: 1.25rem;   // 20px on tablet/mobile\n        line-height: 1.3;\n    }\n\n    h4 {\n        font-size: 1.125rem;  // 18px on tablet/mobile\n        line-height: 1.35;\n    }\n}\n\n/* Small Mobile Adjustments */\n@media (max-width: 480px) {\n    h1 {\n        font-size: 1.5rem;    // 24px on small mobile\n        line-height: 1.25;\n    }\n\n    h2 {\n        font-size: 1.25rem;   // 20px on small mobile\n        line-height: 1.3;\n    }\n\n    h3 {\n        font-size: 1.125rem;  // 18px on small mobile\n        line-height: 1.35;\n    }\n}\n\n/* ========================================\n   Button System - Consistent Interactive Elements\n   ======================================== */\n\n/* Base Button Foundation */\nbutton, .button {\n    /* Layout & Positioning */\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0.75rem 1.5rem;\n\n    /* Visual Styling */\n    border: none;\n    border-radius: 20rem;\n    background: none;\n    outline: none;\n    box-shadow: 0 2px 8px $shadow-medium;\n\n    /* Typography */\n    font-family: 'Inter', sans-serif;\n    font-size: 1rem;\n    font-weight: 500;\n    text-decoration: none;\n\n    /* Interaction */\n    cursor: pointer;\n    transform: translateY(0) scale(1);\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n    /* Hover Effects */\n    &:hover {\n        transform: translateY(-1px) scale(1.02);\n        box-shadow: 0 4px 12px $shadow-heavy;\n    }\n\n    /* Active State */\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px $shadow-heavy;\n        transition: all 0.1s ease;\n    }\n\n    /* Focus State */\n    &:focus {\n        outline: 2px solid rgba(1, 100, 73, 0.5);\n        outline-offset: 2px;\n    }\n\n    /* Disabled State */\n    &:disabled,\n    &.disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n        transform: none !important;\n\n        &:hover {\n            background-color: initial;\n            color: initial;\n        }\n    }\n}\n\n/* ========================================\n   Button Variants - Brand Color System\n   ======================================== */\n\n/* Primary Button - Brand Red */\n.button-primary,\nbutton.primary {\n    background-color: $interactive-secondary;\n    color: $text-inverse;\n\n    &:hover {\n        background-color: $interactive-primary-hover;\n        color: $interactive-primary;\n        box-shadow: 0 4px 12px $shadow-red,\n                    0 8px 24px rgba($brand-red-primary, 0.15);\n    }\n}\n\n/* Secondary Button - Brand Green */\n.button-secondary,\nbutton.secondary {\n    background-color: $interactive-primary-hover;\n    color: $interactive-primary;\n\n\n    &:hover {\n        background-color: $interactive-primary;\n        color: $text-inverse;\n        box-shadow: 0 4px 12px $shadow-green,\n                    0 8px 24px rgba($brand-green-primary, 0.15);\n    }\n}\n\n/* Menu CTA Button - Black with Red Hover */\n.button-menu,\nbutton.menu {\n    background-color: $interactive-neutral;\n    color: $text-inverse;\n\n    &:hover {\n        background-color: $interactive-neutral-hover;\n        color: $text-inverse;\n        box-shadow: 0 4px 12px rgba($brand-red-primary, 0.4),\n                    0 8px 24px rgba($brand-red-primary, 0.2);\n    }\n}\n\n/* ========================================\n   Button Sizes & Modifiers\n   ======================================== */\n\n/* Size Variants */\n.button-sm,\nbutton.sm {\n    padding: 0.5rem 1rem;\n    font-size: 0.875rem;\n}\n\n.button-lg,\nbutton.lg {\n    padding: 1rem 2rem;\n    font-size: 1.125rem;\n}\n\n/* Outline Variants */\n.button-outline {\n    background-color: transparent;\n    border: 2px solid $interactive-primary;\n    color: $interactive-primary;\n    box-shadow: none;\n\n    &:hover {\n        background-color: $interactive-primary;\n        color: $text-inverse;\n        box-shadow: 0 4px 12px $shadow-green,\n                    0 8px 24px rgba($brand-green-primary, 0.15);\n        border-color: $interactive-primary;\n    }\n\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px $shadow-heavy;\n        transition: all 0.1s ease;\n    }\n}\n\n.button-outline-primary {\n    background-color: transparent;\n    border: 2px solid #9C2B32;\n    color: #9C2B32;\n    box-shadow: none;\n\n    &:hover {\n        background-color: #9C2B32;\n        color: white;\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3),\n                    0 8px 24px rgba(156, 43, 50, 0.15);\n        border-color: #9C2B32;\n    }\n}\n\n/* Layout Modifiers */\n.button-full,\nbutton.full {\n    width: 100%;\n}\n\n.button-icon,\nbutton.icon {\n    gap: 0.5rem;\n}\n\n", "/* ========================================\n   OK Tyr Color System\n   Brand colors and semantic color variables\n   ======================================== */\n\n/* ========================================\n   Brand Colors - OK Tyr Identity\n   ======================================== */\n\n// Primary Brand Colors (A1-A4 range)\n$brand-green-primary: #016449;      // Dominant brand color\n$brand-green-light: #cfe7cb;        // Light green for hover states\n$brand-green-dark: #014a37;         // Darker variant for emphasis\n\n// Marketing/Accent Colors (B range)\n$brand-red-primary: #9C2B32;        // Primary accent color\n$brand-red-light: #b8434a;          // Lighter red variant\n$brand-red-dark: #7a2228;           // Darker red variant\n\n// Complementary Colors (C range) - for future use\n$brand-blue: #2B5F9C;               // Complementary blue\n$brand-orange: #D67E37;             // Complementary orange\n$brand-purple: #6B2B9C;             // Complementary purple\n\n/* ========================================\n   Neutral Colors - Grays & Base Colors\n   ======================================== */\n\n// Pure colors\n$color-white: #ffffff;\n$color-black: #000000;\n\n// Gray scale\n$gray-50: #f8f9fa;                  // Lightest gray\n$gray-100: #f5f5f5;                 // Very light gray\n$gray-200: #eef2f6;                 // Light gray (body background)\n$gray-300: #e0e0e0;                 // Light border gray\n$gray-400: #ddd;                    // Border gray\n$gray-500: #999;                    // Medium gray\n$gray-600: #666;                    // Dark gray (secondary text)\n$gray-700: #333;                    // Very dark gray\n$gray-800: #1a1a1a;                 // Primary text color\n$gray-900: #111;                    // Darkest gray\n\n/* ========================================\n   Semantic Color Variables\n   ======================================== */\n\n// Background colors\n$bg-primary: $color-white;\n$bg-secondary: $gray-200;\n$bg-tertiary: $gray-100;\n$bg-overlay: rgba($color-black, 0.6);\n\n// Text colors\n$text-primary: $gray-800;\n$text-secondary: $gray-600;\n$text-inverse: $color-white;\n$text-brand: $brand-green-primary;\n$text-accent: $brand-red-primary;\n\n// Border colors\n$border-light: $gray-300;\n$border-medium: $gray-400;\n$border-focus: $brand-green-primary;\n\n// Interactive colors\n$interactive-primary: $brand-green-primary;\n$interactive-primary-hover: $brand-green-light;\n$interactive-secondary: $brand-red-primary;\n$interactive-secondary-hover: $brand-red-light;\n$interactive-neutral: $color-black;\n$interactive-neutral-hover: $brand-red-primary;\n\n// State colors\n$success: $brand-green-primary;\n$warning: $brand-orange;\n$error: $brand-red-primary;\n$info: $brand-blue;\n\n// Shadow colors\n$shadow-light: rgba($color-black, 0.08);\n$shadow-medium: rgba($color-black, 0.15);\n$shadow-heavy: rgba($color-black, 0.25);\n\n// Brand-specific shadows\n$shadow-green: rgba($brand-green-primary, 0.3);\n$shadow-red: rgba($brand-red-primary, 0.3);", "/* ========================================\n   Navigation System - Floating Frosted Glass Design\n   ======================================== */\n\n@use 'colors' as *;\n\n/* Navigation Container - Fixed Positioning */\n.navbar-container {\n    /* Layout & Positioning */\n    position: fixed;\n    top: 16px;\n    left: 0;\n    right: 0;\n    z-index: 100;\n\n    /* Container Sizing */\n    max-width: 1440px;\n    width: 100%;\n    margin: 0 auto;\n    padding: 0 80px;\n\n    /* Flexbox Layout */\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    /* Smooth Show/Hide Animation */\n    transform: translateY(0);\n    transition: transform 0.4s ease-in-out;\n\n    /* Navigation States */\n    &.nav-hidden {\n        transform: translateY(-200%);\n    }\n\n    &.nav-visible {\n        transform: translateY(0);\n    }\n}\n\n/* Main Navigation Bar */\n.navbar {\n    /* Layout & Positioning */\n    position: relative;\n    width: 100%;\n    max-width: 1250px;\n    display: flex;\n    align-items: center;\n    padding: 16px;\n\n    /* Visual Styling */\n    border-radius: 10rem;\n    color: white;\n    transition: all 0.4s ease;\n\n    /* Fallback Background (for unsupported browsers) */\n    background: rgba(1, 100, 73, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    /* Mobile Expanded State */\n    &.nav-mobile-expanded {\n        /* Layout Changes */\n        border-radius: 1.5rem;\n        flex-direction: column;\n        align-items: stretch;\n        height: 100vh;\n        max-width: none;\n        width: 100%;\n        margin: 0;\n        padding: 0;\n        overflow: hidden;\n        transform-origin: center top;\n\n        /* Header Elements Positioning */\n        .nav-logo,\n        .nav-mobile-toggle {\n            position: absolute;\n            z-index: 10;\n        }\n\n        .nav-logo {\n            top: 16px;\n            left: 16px;\n        }\n\n        .nav-mobile-toggle {\n            top: 16px;\n            right: 16px;\n        }\n\n        /* Mobile Content Area */\n        .nav-mobile-content {\n            flex: 1;\n            display: flex;\n            flex-direction: column;\n            height: 100%;\n            padding: 4rem 1rem 1rem;\n            overflow-y: auto;\n        }\n\n        .nav-mobile-items {\n            flex: 1;\n        }\n    }\n}\n\n/* ========================================\n   Frosted Glass Effect - Enhanced Visual Design\n   ======================================== */\n\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n    .navbar {\n        /* Enhanced Background with Brand Tint */\n        background: rgba(1, 100, 73, 0.6);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n\n        /* Inset Border Effect */\n        -webkit-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        -moz-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1),\n                    0 2px 8px rgba(0, 0, 0, 0.15);\n\n        /* Frosted Glass Backdrop Filter */\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n\n        /* Enhanced Mobile Expanded State */\n        &.nav-mobile-expanded {\n            background: rgba(0, 0, 0, 0.6);\n            -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n            backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n        }\n    }\n}\n\n/* ========================================\n   Navigation Content Layout\n   ======================================== */\n\n/* Logo Section */\n.nav-logo {\n    flex: 0 0 auto;\n    max-width: 40px;\n    max-height: 40px;\n\n    img {\n        width: 100%;\n        height: 100%;\n        object-fit: contain;\n    }\n}\n\n/* Navigation Items Collection */\n.nav-collection {\n    display: flex;\n    align-items: center;\n    flex: 1 1 auto;\n    justify-content: center;\n}\n\n/* Call-to-Action Section */\n.nav-cta {\n    flex: 0 0 auto;\n\n    a {\n        /* Button Menu Styles */\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        background-color: $interactive-neutral;\n        color: $text-inverse;\n        padding: 8px 16px;\n        border: none;\n        border-radius: 20rem;\n        font-family: 'Inter', sans-serif;\n        font-size: 1rem;\n        font-weight: 500;\n        text-decoration: none;\n        cursor: pointer;\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        box-shadow: 0 2px 8px $shadow-medium;\n        transform: translateY(0) scale(1);\n\n        &:hover {\n            background-color: $interactive-neutral-hover;\n            color: $text-inverse;\n            transform: translateY(-1px) scale(1.02);\n            box-shadow: 0 4px 12px rgba($brand-red-primary, 0.4),\n                        0 8px 24px rgba($brand-red-primary, 0.2);\n        }\n\n        &:active {\n            transform: translateY(0) scale(0.98);\n            box-shadow: 0 1px 4px $shadow-heavy;\n            transition: all 0.1s ease;\n        }\n\n        &:focus {\n            outline: 2px solid rgba(1, 100, 73, 0.5);\n            outline-offset: 2px;\n        }\n    }\n}\n\n/* Navigation Item Container */\n.nav-item {\n    position: relative;\n}\n\n/* Override Global Button Styles for Navbar */\n.navbar button {\n    box-shadow: none;\n}\n\n/* ========================================\n   Navigation Links & Interactive Elements\n   ======================================== */\n\n/* Base Navigation Links & Toggles */\n.nav-link,\n.nav-toggle {\n    /* Layout */\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n    padding: 0.5rem 1rem;\n\n    /* Reset Styles */\n    background: none;\n    border: none;\n    text-decoration: none;\n    color: inherit;\n\n    /* Typography */\n    font-size: 1rem;\n    font-weight: 500;\n\n    /* Visual */\n    border-radius: 0.5rem;\n    cursor: pointer;\n    transition: background-color 0.2s ease;\n\n    /* Override Global Button Styles */\n    box-shadow: none !important;\n    transform: none !important;\n\n    /* Hover State */\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    /* Active State */\n    &:active {\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    /* Focus State */\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Arrow animation for dropdowns (chevron to minus)\n.nav-toggle,\n.nav-toggle-level2 {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 10px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s;\n            right: 0.5rem; // Position for desktop dropdowns\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 10px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n        right: 0.5rem; // Position for desktop dropdowns\n    }\n}\n\n// Dropdown containers\n.nav-dropdown {\n    position: absolute;\n    top: 100%;\n    left: 0;\n    min-width: 200px;\n    background: rgba(0, 0, 0, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    border-radius: 0.5rem;\n    padding: 0.5rem 0;\n    margin-top: 0.5rem;\n    opacity: 0;\n    visibility: hidden;\n    transform: translateY(-10px);\n    transition: all 0.2s ease;\n    z-index: 1000;\n\n    // Backdrop filter for supported browsers\n    @supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    }\n\n    // Show dropdown when parent is active\n    .nav-item:hover &,\n    .nav-item.nav-active & {\n        opacity: 1;\n        visibility: visible;\n        transform: translateY(0);\n    }\n}\n\n// Level 2 dropdown positioning\n.nav-dropdown-level2 {\n    top: 0;\n    left: 100%;\n    margin-top: 0;\n    margin-left: 0.5rem;\n}\n\n// Dropdown items\n.nav-dropdown-item {\n    position: relative;\n}\n\n.nav-dropdown-link,\n.nav-toggle-level2 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    padding: 0.75rem 1rem;\n    text-decoration: none;\n    color: inherit;\n    background: none;\n    border: none;\n    cursor: pointer;\n    font-size: 0.9rem;\n    transition: background-color 0.2s ease;\n\n    // Override global button styles\n    box-shadow: none !important;\n    transform: none !important;\n    border-radius: 0;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: -2px;\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n}\n\n/* This section is now handled above in the reorganized .nav-cta section */\n\n// Mobile Menu Toggle Button\n.nav-mobile-toggle {\n    display: none;\n    align-items: center;\n    gap: 0.5rem;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    padding: 0.5rem;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n    margin-left: auto; // Push to the right side\n\n    // Override global button styles\n    box-shadow: none !important;\n    transform: none !important;\n    font-size: inherit;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n.hamburger-icon {\n    display: flex;\n    flex-direction: column;\n    gap: 3px;\n    width: 18px;\n    height: 14px;\n    order: 2; // Place icon after text\n}\n\n.hamburger-line {\n    width: 100%;\n    height: 2px;\n    background-color: currentColor;\n    border-radius: 1px;\n    transition: all 0.3s ease;\n}\n\n.nav-mobile-toggle-text {\n    font-size: 0.9rem;\n    font-weight: 500;\n    order: 1; // Place text before icon\n}\n\n// Hamburger animation when menu is open (hamburger to minus)\n.nav-mobile-toggle[aria-expanded=\"true\"] {\n    .hamburger-line {\n        &:nth-child(1) {\n            opacity: 0;\n            transform: translateY(5px);\n        }\n        &:nth-child(2) {\n            // Middle line becomes the minus\n            transform: scaleX(1);\n        }\n        &:nth-child(3) {\n            opacity: 0;\n            transform: translateY(-5px);\n        }\n    }\n}\n\n// Mobile Menu Content (inside expanded nav)\n.nav-mobile-content {\n    display: none;\n    flex-direction: column;\n    flex: 1;\n    width: 100%;\n    opacity: 0;\n    transform: translateY(20px);\n    transition: all 0.4s ease 0.2s; // Delay for smooth entrance\n\n    // Show when nav is expanded\n    .nav-mobile-expanded & {\n        display: flex;\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.nav-mobile-items {\n    flex: 1;\n    overflow-y: auto;\n}\n\n// Mobile Menu Items\n.nav-mobile-item {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    padding: 0 1rem;\n    opacity: 0;\n    transform: translateY(10px);\n    animation: none; // Reset animation initially\n\n    // Apply animation when nav is expanded\n    .nav-mobile-expanded & {\n        animation: fadeInUp 0.4s ease forwards;\n\n        // Stagger animation for each item\n        &:nth-child(1) { animation-delay: 0.3s; }\n        &:nth-child(2) { animation-delay: 0.4s; }\n        &:nth-child(3) { animation-delay: 0.5s; }\n        &:nth-child(4) { animation-delay: 0.6s; }\n        &:nth-child(5) { animation-delay: 0.7s; }\n        &:nth-child(6) { animation-delay: 0.8s; }\n        &:nth-child(7) { animation-delay: 0.9s; }\n        &:nth-child(8) { animation-delay: 1.0s; }\n    }\n}\n\n@keyframes fadeInUp {\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n// Split header for items with submenus\n.nav-mobile-item-header,\n.nav-mobile-subitem-header {\n    display: flex;\n    align-items: center;\n    width: 100%;\n}\n\n.nav-mobile-item-header .nav-mobile-link,\n.nav-mobile-subitem-header .nav-mobile-link {\n    flex: 1;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Toggle buttons for submenus\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 48px;\n    height: 48px;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n\n    // Override global button styles\n    padding: 0 !important;\n    box-shadow: none !important;\n    transform: none !important;\n    font-size: inherit;\n    font-weight: inherit;\n    font-family: inherit;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        // Override global button hover effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:active {\n        // Override global button active effects\n        transform: none !important;\n        box-shadow: none !important;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Icon swap animation: chevron to minus (active)\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 12px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s; // Slight delay for smooth transition\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 12px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n    }\n}\n\n\n// Regular mobile links (no submenus)\n.nav-mobile-link {\n    display: flex;\n    align-items: center;\n    width: 100%;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n.nav-mobile-link-level1 {\n    font-size: 1.2rem;\n    font-weight: 600;\n}\n\n.nav-mobile-link-level2,\n.nav-mobile-subitem-header .nav-mobile-link {\n    padding-left: 1rem;\n    font-size: 1rem;\n    font-weight: 400;\n}\n\n.nav-mobile-link-level3 {\n    padding-left: 2rem;\n    font-size: 0.9rem;\n    font-weight: 400;\n}\n\n// Mobile Submenus\n.nav-mobile-submenu,\n.nav-mobile-subsubmenu {\n    max-height: 0;\n    overflow: hidden;\n    transition: max-height 0.3s ease;\n\n    &.nav-mobile-submenu-active {\n        max-height: 500px; // Adjust based on content\n    }\n}\n\n.nav-mobile-subitem {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n// Mobile CTA at bottom\n.nav-mobile-cta {\n    margin-top: auto;\n    padding: 1rem;\n    width: 100%;\n    opacity: 0;\n    text-align: center;\n    transform: translateY(20px);\n    animation: none; // Reset animation initially\n\n    // Apply animation when nav is expanded\n    .nav-mobile-expanded & {\n        animation: fadeInUp 0.4s ease 1.1s forwards; // Animate in last\n    }\n}\n\n.nav-mobile-cta-button {\n    /* Button Menu + Full Width Styles */\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    background-color: #000000;\n    color: white;\n    padding: 16px 24px;\n    border: none;\n    border-radius: 20rem;\n    font-family: 'Inter', sans-serif;\n    font-size: 1rem;\n    font-weight: 500;\n    text-decoration: none;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    transform: translateY(0) scale(1);\n    width: 100%;\n\n    &:hover {\n        background-color: #9C2B32;\n        color: white;\n        transform: translateY(-1px) scale(1.02);\n        box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4),\n                    0 8px 24px rgba(156, 43, 50, 0.2);\n    }\n\n    &:active {\n        transform: translateY(0) scale(0.98);\n        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n        transition: all 0.1s ease;\n    }\n\n    &:focus {\n        outline: 2px solid rgba(1, 100, 73, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n//Decrease padding after 1600px\n@media (max-width: 1600px) {\n    .navbar-container {\n        padding: 0 20px;\n    }\n}\n\n//Remove CTA after 1200px\n@media (max-width: 1200px) {\n    .nav-cta {\n        display: none;\n    }\n}\n\n// Mobile navigation breakpoint\n@media (max-width: 1024px) {\n    .nav-desktop {\n        display: none;\n    }\n\n    .nav-mobile-toggle {\n        display: flex;\n    }\n\n    // Ensure proper flex layout in mobile mode\n    .navbar {\n        justify-content: space-between;\n\n        .nav-logo {\n            flex: 0 0 auto;\n        }\n\n        // When expanded, take full viewport\n        &.nav-mobile-expanded {\n            position: fixed;\n            top: 20px;\n            left: 20px;\n            right: 20px;\n            bottom: 20px;\n            max-width: none;\n            width: auto;\n            height: auto;\n            z-index: 1000;\n        }\n    }\n\n    // Adjust container for expanded state\n    .navbar-container {\n        &.nav-expanded {\n            position: fixed;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            padding: 20px;\n            z-index: 1000;\n        }\n    }\n}\n\n@media (max-width: 768px) {\n    .navbar-container {\n        padding: 0 20px; // Reduced padding on mobile\n    }\n\n    nav {\n        padding: 12px; // Reduced nav padding on mobile\n    }\n}\n\n// Enhanced hover effects for desktop\n@media (min-width: 769px) {\n    .nav-item {\n        // Hover to show dropdown\n        &:hover .nav-dropdown {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n\n        // Nested hover for level 2\n        .nav-dropdown-item:hover .nav-dropdown-level2 {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n    }\n}\n\n// Animation improvements\n.nav-dropdown {\n    // Smooth entrance animation\n    animation-duration: 0.2s;\n    animation-timing-function: ease-out;\n    animation-fill-mode: both;\n\n    &.nav-dropdown-level2 {\n        animation-delay: 0.1s; // Slight delay for nested dropdowns\n    }\n}\n\n// Focus management for accessibility\n.nav-toggle:focus,\n.nav-toggle-level2:focus,\n.nav-dropdown-link:focus {\n    outline: 2px solid rgba(255, 255, 255, 0.8);\n    outline-offset: 2px;\n    background-color: rgba(255, 255, 255, 0.15);\n}", "/* ========================================\n   Hero Section - Main landing area\n   ======================================== */\n\n@use 'colors' as *;\n\n.hero {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n\n    h1 {\n        font-size: 3em;\n    }\n    @media (max-width: 768px) {\n        h1 {\n            font-size: 2em;\n        }\n    }\n\n    /* Hero Content Container */\n    &-content {\n        height: 90vh;\n        max-height: 800px;\n        min-height: 600px;\n        max-width: 1440px;\n        width: 100%;\n        position: relative;\n        overflow: hidden;\n        border-radius: 24px;\n        padding: 2rem;\n\n        /* Layout */\n        display: flex;\n        flex-direction: column;\n        justify-content: flex-end;\n        align-items: flex-start;\n\n        /* Background & Visual Effects */\n        background-size: cover !important;\n        background-position: center center !important;\n        background: linear-gradient(180deg, rgba($color-black, 0) 0%, $bg-overlay 100%),\n                    linear-gradient(0deg, $bg-overlay, $bg-overlay),\n                    linear-gradient(0deg, $brand-green-primary, $brand-green-primary);\n        background-blend-mode: normal, normal, color, normal, normal;\n\n        /* Typography */\n        color: $text-inverse;\n    }\n\n    /* Call-to-Action Section */\n    &-cta {\n        margin-top: 2rem;\n        display: flex;\n        gap: 1rem;\n        flex-wrap: wrap;\n    }\n}\n\n/* ========================================\n   Banner Content Area\n   ======================================== */\n\n.banner {\n    width: 50vw;\n    padding: 2rem;\n\n    /* Typography Styles - inherit from global styles */\n\n    /* Mobile Responsive */\n    @media (max-width: 768px) {\n        width: 100%;\n        padding: 0;\n    }\n}\n\n", "/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */\n\n@use 'colors' as *;\n.highlighted-updates {\n    display: grid;\n    gap: 2rem;\n    margin-bottom: 3rem;\n\n    /* Mobile: Stack vertically with calendar first */\n    grid-template-columns: 1fr;\n    grid-template-areas:\n        \"calendar\"\n        \"news\";\n\n    /* Desktop: Side by side with 2:1 ratio */\n    @media (min-width: 768px) {\n        grid-template-columns: 2fr 1fr;\n        grid-template-areas: \"news calendar\";\n    }\n\n    h2 {\n        grid-column: 1 / -1;\n        margin-bottom: 1.5rem;\n    }\n}\n\n/* News Section - Featured article with overlay */\n.highlighted-updates-news {\n    grid-area: news;\n\n    article {\n        position: relative;\n        height: 100%;\n        border-radius: 16px;\n        overflow: hidden;\n        box-shadow: 0 4px 20px $shadow-light;\n        transition: all 0.3s ease;\n\n        &:hover {\n            transform: translateY(-4px);\n            box-shadow: 0 8px 30px $shadow-medium;\n        }\n\n        a {\n            display: block;\n            text-decoration: none;\n            color: inherit;\n            height: 100%;\n\n            > div {\n                position: relative;\n                min-height: 300px;\n                height: 100%;\n\n                @media (min-width: 768px) {\n                    min-height: 400px;\n                }\n            }\n\n            img {\n                width: 100%;\n                height: 100%;\n                object-fit: cover;\n                position: absolute;\n                top: 0;\n                left: 0;\n            }\n\n            /* Fallback for missing images */\n            > div > div:first-child:not(.highlighted-news-content) {\n                background: $brand-green-primary;\n                width: 100%;\n                height: 100%;\n                position: absolute;\n                top: 0;\n                left: 0;\n            }\n        }\n    }\n}\n\n/* News content overlay */\n.highlighted-news-content {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    background: linear-gradient(transparent, $bg-overlay);\n    color: $text-inverse;\n    padding: 2rem;\n\n    time {\n        display: block;\n        font-size: 0.875rem;\n        opacity: 0.9;\n        margin-bottom: 0.5rem;\n        font-weight: 500;\n    }\n\n    h2 {\n        margin: 0;\n        font-size: 1.5rem;\n        line-height: 1.3;\n\n        @media (min-width: 768px) {\n            font-size: 1.875rem;\n        }\n    }\n\n    p {\n        margin: 0.75rem 0 0 0;\n        opacity: 0.9;\n        line-height: 1.5;\n    }\n}\n\n/* Calendar Section - Stacked event layout */\n.highlighted-updates-calendar {\n    grid-area: calendar;\n\n    .calendar-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 1.5rem;\n\n        h3 {\n            margin: 0;\n            color: $text-brand;\n        }\n\n        a {\n            color: $text-accent;\n            text-decoration: none;\n            font-weight: 500;\n            font-size: 0.875rem;\n\n            &:hover {\n                text-decoration: underline;\n            }\n        }\n    }\n\n    /* Events container - compact single container */\n    .calendar-events {\n        background: $bg-primary;\n        border-radius: 12px;\n        padding: 1rem;\n        margin-bottom: 1rem;\n    }\n\n    /* Individual event items - compact layout */\n    .calendar-event {\n        display: flex;\n        align-items: center;\n        gap: 0.75rem;\n        padding: 0.75rem 0;\n        text-decoration: none;\n        color: inherit;\n        border-bottom: 1px solid $border-light;\n        transition: all 0.2s ease;\n\n        &:last-child {\n            border-bottom: none;\n        }\n\n        &:hover {\n            background: rgba($brand-green-primary, 0.05);\n            margin: 0 -1rem;\n            padding-left: 1rem;\n            padding-right: 1rem;\n            border-radius: 8px;\n        }\n    }\n\n    /* Date component with card-stack effect for multiple events */\n    .event-date {\n        flex-shrink: 0;\n        width: 50px;\n        height: 50px;\n        background: $brand-green-primary;\n        border-radius: 8px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: $text-inverse;\n        position: relative;\n\n        /* Card stack effect for multiple events */\n        &.multiple-events {\n            &::before {\n                content: '';\n                position: absolute;\n                top: -3px;\n                left: -3px;\n                right: 3px;\n                bottom: 3px;\n                background: rgba($brand-green-primary, 0.3);\n                border-radius: 8px;\n                z-index: -1;\n            }\n\n            &::after {\n                content: '';\n                position: absolute;\n                top: -6px;\n                left: -6px;\n                right: 6px;\n                bottom: 6px;\n                background: rgba($brand-green-primary, 0.15);\n                border-radius: 8px;\n                z-index: -2;\n            }\n        }\n\n        span:first-child {\n            font-size: 1.125rem;\n            font-weight: 700;\n            line-height: 1;\n        }\n\n        span:last-child {\n            font-size: 0.625rem;\n            font-weight: 500;\n            text-transform: uppercase;\n            opacity: 0.9;\n        }\n    }\n\n    /* Event details */\n    .event-details {\n        flex: 1;\n        min-width: 0;\n\n        h4 {\n            margin: 0 0 0.125rem 0;\n            font-size: 0.875rem;\n            font-weight: 600;\n            color: $text-primary;\n            line-height: 1.3;\n\n            /* Truncate long titles */\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n        }\n\n        .event-meta {\n            display: flex;\n            gap: 0.5rem;\n\n            span {\n                font-size: 0.75rem;\n                color: $text-secondary;\n\n                &:first-child {\n                    font-weight: 500;\n                    color: $text-brand;\n                }\n            }\n        }\n    }\n\n    /* \"See full calendar\" link - use secondary button */\n    .calendar-footer {\n        text-align: center;\n        padding: 8px 0;\n    }\n}\n\n/* News Carousel - Mobile-First Implementation */\n.news-carousel {\n    padding: 3rem 0;\n\n    .container {\n        max-width: 1440px;\n        margin: 0 auto;\n        padding: 0 1rem;\n    }\n\n    /* Header with navigation */\n    .carousel-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 2rem;\n\n        h3 {\n            margin: 0;\n            color: $text-brand;\n            font-size: 1.5rem;\n        }\n\n        .carousel-nav {\n            display: flex;\n            flex-direction: row;\n            gap: 0.5rem;\n\n            @media (max-width: 640px) {\n                display: none; /* Hide on mobile, rely on touch scrolling */\n            }\n        }\n    }\n\n    /* Navigation buttons - Ultra simple with SVG arrows */\n    .carousel-btn {\n        width: 44px;\n        height: 44px;\n        background: $bg-primary;\n        border: none;\n        border-radius: 50%;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: background-color 0.2s ease;\n\n        /* Override global button styles */\n        padding: 0;\n        box-shadow: none;\n        transform: none;\n        outline: none;\n\n        &:hover:not(:disabled) {\n            background-color: $interactive-primary-hover;\n            color: $interactive-primary;\n            box-shadow: none;\n            transform: none;\n        }\n\n        &:active {\n            box-shadow: none;\n            transform: none;\n        }\n\n        &:focus {\n            box-shadow: none;\n            outline: none;\n        }\n\n        &:disabled {\n            opacity: 0.4;\n            cursor: not-allowed;\n            box-shadow: none;\n            transform: none;\n        }\n\n        /* SVG arrow icons */\n        &::before {\n            content: '';\n            width: 16px;\n            height: 16px;\n            background-color: $color-black;\n            transition: background-color 0.2s ease;\n        }\n\n        &:hover:not(:disabled)::before {\n            background-color: $interactive-primary;\n        }\n\n        /* Previous arrow (left) */\n        &--prev::before {\n            mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E\") no-repeat center;\n            mask-size: contain;\n        }\n\n        /* Next arrow (right) */\n        &--next::before {\n            mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z'/%3E%3C/svg%3E\") no-repeat center;\n            mask-size: contain;\n        }\n    }\n\n    /* Carousel wrapper - allows visible overflow for shadows/effects */\n    .carousel-wrapper {\n        position: relative;\n        overflow: visible; /* Allow content to overflow for shadows/effects */\n        padding: 1rem 0;\n    }\n\n    /* Scrollable container */\n    .carousel-scroll {\n        display: flex;\n        gap: 1.5rem;\n        overflow-x: auto;\n        /* Note: overflow-y is automatically set to hidden when overflow-x is auto */\n        scroll-behavior: smooth;\n        scroll-snap-type: x mandatory;\n        -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */\n        scrollbar-width: none; /* Firefox */\n        -ms-overflow-style: none; /* IE/Edge */\n        padding: 1rem;\n        margin: -1rem;\n\n        /* Hide scrollbar in WebKit browsers */\n        &::-webkit-scrollbar {\n            display: none;\n        }\n\n        @media (max-width: 640px) {\n            gap: 1rem;\n            padding: 1rem 0.5rem;\n            margin: -1rem -0.5rem;\n        }\n    }\n\n    /* Card base styles */\n    .carousel-card {\n        flex: 0 0 320px;\n        background: $bg-primary;\n        border-radius: 8px;\n\n        box-shadow: 0 2px 12px $shadow-light;\n        scroll-snap-align: start;\n        transition: all 0.3s ease;\n\n        @media (max-width: 768px) {\n            flex: 0 0 280px;\n        }\n\n        @media (max-width: 480px) {\n            flex: 0 0 260px;\n        }\n\n        &:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 8px 24px rgba($color-black, 0.12);\n        }\n\n        a {\n            display: block;\n            text-decoration: none;\n            color: inherit;\n            height: 100%;\n        }\n    }\n\n    /* Card image */\n    .card-image {\n        height: 200px;\n        overflow: hidden;\n        background: $gray-50;\n        position: relative;\n\n        @media (max-width: 480px) {\n            height: 160px;\n        }\n\n        img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n        }\n\n        .card-placeholder {\n            width: 100%;\n            height: 100%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            background: $brand-green-primary;\n            color: $text-inverse;\n\n            svg {\n                opacity: 0.6;\n            }\n        }\n    }\n\n    /* Card content */\n    .card-content {\n        padding: 1.25rem;\n\n        @media (max-width: 480px) {\n            padding: 1rem;\n        }\n\n        time {\n            display: block;\n            font-size: 0.75rem;\n            color: $text-secondary;\n            margin-bottom: 0.5rem;\n            font-weight: 500;\n            text-transform: uppercase;\n            letter-spacing: 0.5px;\n        }\n\n        h4 {\n            margin: 0 0 0.75rem 0;\n            font-size: 1rem;\n            font-weight: 600;\n            line-height: 1.4;\n            color: $text-primary;\n\n            /* Limit to 2 lines */\n            display: -webkit-box;\n            -webkit-line-clamp: 2;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n\n        p {\n            margin: 0;\n            font-size: 0.875rem;\n            color: $text-secondary;\n            line-height: 1.5;\n\n            /* Limit to 3 lines */\n            display: -webkit-box;\n            -webkit-line-clamp: 3;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n        }\n    }\n\n    /* Archive card */\n    .carousel-card--archive {\n        background: $brand-red-primary;\n        color: $text-inverse;\n\n        .archive-content {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            justify-content: center;\n            text-align: center;\n            padding: 2rem 1.5rem;\n            height: 100%;\n            min-height: 300px;\n\n            @media (max-width: 480px) {\n                padding: 1.5rem 1rem;\n                min-height: 260px;\n            }\n        }\n\n        .archive-icon {\n            margin-bottom: 1rem;\n            opacity: 0.9;\n\n            svg {\n                width: 48px;\n                height: 48px;\n            }\n        }\n\n        h4 {\n            margin: 0 0 0.75rem 0;\n            font-size: 1.25rem;\n            font-weight: 600;\n            color: $text-inverse;\n        }\n\n        p {\n            margin: 0 0 1rem 0;\n            font-size: 0.875rem;\n            opacity: 0.9;\n            line-height: 1.5;\n            color: $text-inverse;\n        }\n\n        .archive-cta {\n            font-size: 0.875rem;\n            font-weight: 500;\n            opacity: 0.8;\n            color: $text-inverse;\n        }\n    }\n}", "@use 'colors' as *;\n\n.partners {\n    margin-top: 2rem;\n    padding: 3rem 0;\n    background-color: $bg-tertiary;\n    border-top: 1px solid $border-medium;\n\n}\n\n.partners-header {\n    text-align: center;\n}\n\n.partners-list {\n    display: flex;\n    justify-content: center;\n    align-items: center;;\n    flex-wrap: nowrap;\n    gap: 3rem;\n    margin-top: 2rem;\n\n    .partner-name {\n        font-weight: bold;\n        font-size: 1.2rem;\n    }\n\n    img {\n        max-width: 150px;\n        max-height: 60px;\n        filter: grayscale(100%);\n    }\n}\n\nfooter {\n    background-color: $brand-green-primary;\n    padding: 3rem 0;\n\n    .container {\n        display: grid;\n        grid-template-columns: 1fr 1fr 1fr;\n        gap: 2rem;\n        grid-template-areas:\n            \"info contact finance\"\n            \"copyright copyright copyright\";\n    }\n    .club-info {\n        grid-area: info;\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n\n        img {\n            max-width: 100%;\n            height: auto;\n        }\n\n        h3 {\n            font-size: 1.2rem;\n            font-weight: 600;\n            margin: 0;\n            color: $text-primary;\n        }\n\n        p {\n            font-size: 0.875rem;\n            line-height: 1.5;\n            color: $text-secondary;\n        }\n    }\n\n    .social-links {\n        grid-area: social;\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n\n        a {\n            display: flex;\n            align-items: center;\n            gap: 0.75rem;\n            color: $text-primary;\n            text-decoration: none;\n            font-size: 0.875rem;\n            line-height: 1.5;\n            font-weight: 500;\n            transition: all 0.3s ease;\n\n            &:hover {\n                color: $brand-green-light;\n\n                .social-icon {\n                    background-color: $brand-green-light;\n                    color: $brand-green-primary;\n                    transform: scale(1.05);\n                }\n            }\n        }\n\n        .social-icon {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            width: 40px;\n            height: 40px;\n            background-color: rgba($color-white, 0.1);\n            border-radius: 12px; // Squircle effect\n            color: $text-primary;\n            transition: all 0.3s ease;\n            flex-shrink: 0;\n\n            svg {\n                width: 20px;\n                height: 20px;\n            }\n        }\n    }\n\n    .contact {\n        grid-area: contact;\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n\n        h3 {\n            font-size: 1.2rem;\n            font-weight: 600;\n            margin: 0;\n            color: $text-primary;\n        }\n\n        .contact-item {\n            display: flex;\n            align-items: flex-start;\n            gap: 0.75rem;\n\n            .contact-icon {\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                width: 32px;\n                height: 32px;\n                background-color: $brand-green-light;\n                border-radius: 8px;\n                color: $brand-green-primary;\n                flex-shrink: 0;\n                margin-top: 0.125rem; // Slight offset to align with text\n\n                svg {\n                    width: 16px;\n                    height: 16px;\n                }\n            }\n\n            div {\n                flex: 1;\n\n                p {\n                    margin: 0 0 0.25rem 0;\n                    font-size: 0.875rem;\n                    font-weight: 600;\n                    color: $text-primary;\n                }\n\n                address {\n                    font-style: normal;\n\n                    p {\n                        margin: 0;\n                        font-size: 0.875rem;\n                        line-height: 1.4;\n                        color: $text-secondary;\n                        font-weight: 400;\n                    }\n                }\n\n                a {\n                    color: $text-primary;\n                    text-decoration: none;\n                    font-size: 0.875rem;\n                    line-height: 1.4;\n                    font-weight: 400;\n\n                    &:hover {\n                        color: $brand-green-primary;\n                        text-decoration: underline;\n                    }\n                }\n            }\n        }\n    }\n\n    .finance {\n        grid-area: finance;\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n\n        h3 {\n            font-size: 1.2rem;\n            font-weight: 600;\n            margin: 0;\n            color: $text-primary;\n        }\n\n        .contact-item {\n            display: flex;\n            align-items: flex-start;\n            gap: 0.75rem;\n\n            .contact-icon {\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                width: 32px;\n                height: 32px;\n                background-color: $brand-green-light;\n                border-radius: 8px;\n                color: $brand-green-primary;\n                flex-shrink: 0;\n                margin-top: 0.125rem; // Slight offset to align with text\n\n                svg {\n                    width: 16px;\n                    height: 16px;\n                }\n            }\n\n            div {\n                flex: 1;\n\n                p {\n                    margin: 0 0 0.25rem 0;\n                    font-size: 0.875rem;\n                    font-weight: 600;\n                    color: $text-primary;\n                }\n\n                address {\n                    font-style: normal;\n\n                    p {\n                        margin: 0;\n                        font-size: 0.875rem;\n                        line-height: 1.4;\n                        color: $text-secondary;\n                        font-weight: 400;\n                    }\n                }\n\n                a {\n                    color: $text-primary;\n                    text-decoration: none;\n                    font-size: 0.875rem;\n                    line-height: 1.4;\n                    font-weight: 400;\n\n                    &:hover {\n                        color: $brand-green-primary;\n                        text-decoration: underline;\n                    }\n                }\n            }\n        }\n    }\n\n    .copyright {\n        grid-area: copyright;\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n\n        p {\n            font-size: 0.875rem;\n            line-height: 1.5;\n            color: $text-secondary;\n        }\n\n        div {\n            display: flex;\n            align-items: center;\n            gap: 0.5rem;\n            color: $text-primary;\n            text-decoration: none;\n            font-size: 0.875rem;\n            line-height: 1.5;\n            font-weight: 500;\n\n            &:hover {\n                text-decoration: underline;\n            }\n        }\n    }\n\n}\n"], "names": [], "sourceRoot": ""}